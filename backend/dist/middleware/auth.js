"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ownershipMiddleware = exports.mainAdminMiddleware = exports.adminMiddleware = exports.isMainAdmin = exports.authMiddleware = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const Admin_1 = require("../models/Admin");
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
// Original middleware (renamed for backward compatibility)
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        if (!token) {
            return res.status(401).json({ message: 'No token provided' });
        }
        const decoded = jsonwebtoken_1.default.verify(token, JWT_SECRET);
        // Verify admin still exists
        const admin = await Admin_1.Admin.findById(decoded.id);
        if (!admin) {
            return res.status(401).json({ message: 'Admin not found' });
        }
        req.user = decoded;
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            return res.status(401).json({ message: 'Invalid token' });
        }
        console.error('Auth middleware error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.authenticateToken = authenticateToken;
// New middleware for tests
const authMiddleware = (req, res, next) => {
    try {
        // Get token from Authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader) {
            return res.status(401).json({ message: 'No token provided' });
        }
        // Check token format
        const parts = authHeader.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return res.status(401).json({ message: 'Invalid token format' });
        }
        const token = parts[1];
        // Verify token
        const decoded = jsonwebtoken_1.default.verify(token, JWT_SECRET);
        // Attach user info to request
        req.user = decoded;
        next();
    }
    catch (error) {
        return res.status(401).json({ message: 'Invalid token' });
    }
};
exports.authMiddleware = authMiddleware;
// Original middleware (renamed for backward compatibility)
const isMainAdmin = (req, res, next) => {
    var _a;
    if (!((_a = req.user) === null || _a === void 0 ? void 0 : _a.isMainAdmin)) {
        return res.status(403).json({ message: 'Access denied. Main admin privileges required.' });
    }
    next();
};
exports.isMainAdmin = isMainAdmin;
// New middleware for tests
const adminMiddleware = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
    }
    if (req.user.role !== 'admin') {
        return res.status(403).json({ message: 'Access denied' });
    }
    next();
};
exports.adminMiddleware = adminMiddleware;
// New middleware for tests
const mainAdminMiddleware = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
    }
    if (req.user.role !== 'admin' || !req.user.isMainAdmin) {
        return res.status(403).json({ message: 'Main admin privileges required' });
    }
    next();
};
exports.mainAdminMiddleware = mainAdminMiddleware;
// New middleware for tests
const ownershipMiddleware = (resourceType) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return res.status(401).json({ message: 'Authentication required' });
            }
            const resourceId = parseInt(req.params.id);
            if (isNaN(resourceId)) {
                return res.status(400).json({ message: 'Invalid resource ID' });
            }
            // Admin users can access any resource
            if (req.user.role === 'admin') {
                return next();
            }
            // For non-admin users, check ownership
            let resource = null;
            // Get the resource based on its type
            switch (resourceType) {
                case 'scholarship':
                    resource = await prisma.scholarship.findUnique({
                        where: { id: resourceId }
                    });
                    break;
                // Add other resource types as needed
                default:
                    return res.status(500).json({ message: `Unknown resource type: ${resourceType}` });
            }
            if (!resource) {
                return res.status(404).json({ message: 'Resource not found' });
            }
            // Check if the user is the owner of the resource
            if (resource.createdBy !== req.user.id) {
                return res.status(403).json({ message: 'Not authorized' });
            }
            next();
        }
        catch (error) {
            console.error('Ownership middleware error:', error);
            return res.status(500).json({ message: 'Server error' });
        }
    };
};
exports.ownershipMiddleware = ownershipMiddleware;
