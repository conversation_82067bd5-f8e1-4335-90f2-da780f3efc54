"use strict";
/**
 * DATABASE MIGRATION NOTICE
 *
 * This application has been fully migrated from Sequelize to PostgreSQL.
 * All database operations now use direct PostgreSQL queries for improved performance.
 *
 * The migration was completed and Prisma has been completely removed.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
// Load environment variables first
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const morgan_1 = __importDefault(require("morgan"));
const path_1 = __importDefault(require("path"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const compression_1 = __importDefault(require("compression"));
const helmet_1 = __importDefault(require("helmet"));
const database_1 = require("./config/database");
const Admin_1 = require("./models/Admin");
const envValidator_1 = __importDefault(require("./utils/envValidator"));
const csrf_middleware_1 = require("./middleware/csrf.middleware");
const apiCache_middleware_1 = __importDefault(require("./middleware/apiCache.middleware"));
const validation_middleware_1 = __importDefault(require("./middleware/validation.middleware"));
const rateLimiting_middleware_1 = __importDefault(require("./middleware/rateLimiting.middleware"));
// Import routes
const auth_1 = __importDefault(require("./routes/auth"));
const auth_cookie_routes_1 = __importDefault(require("./routes/auth.cookie.routes"));
const scholarship_routes_1 = __importDefault(require("./routes/scholarship.routes"));
const user_routes_1 = __importDefault(require("./routes/user.routes"));
// import adminCookieRoutes from "./routes/admin.cookie.routes"; // Temporarily disabled
// import adminPasswordRoutes from './routes/admin.password.routes'; // Temporarily disabled
const twoFactor_routes_1 = __importDefault(require("./routes/twoFactor.routes"));
const messages_1 = __importDefault(require("./routes/messages"));
const newsletter_1 = __importDefault(require("./routes/newsletter"));
// Validate and load environment variables
if (!(0, envValidator_1.default)()) {
    console.error('Environment validation failed. Exiting application.');
    process.exit(1);
}
// Log application startup information
console.info(`Starting MaBourse backend in ${process.env.NODE_ENV} mode`);
console.info(`Server port: ${process.env.PORT}`);
console.info(`Database: ${(_a = process.env.DATABASE_URL) === null || _a === void 0 ? void 0 : _a.replace(/:[^:]*@/, ':****@')}`);
// Initialize PostgreSQL database connection
const db = (0, database_1.initializeDatabase)();
// Create Express app
const app = (0, express_1.default)();
// Middleware
// Configure CORS based on environment
const corsOrigins = process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000', 'http://localhost:3001'];
app.use((0, cors_1.default)({
    origin: corsOrigins, // Use specific origins instead of true for better security
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token']
}));
// Log CORS configuration
console.info(`CORS configured for origins: ${corsOrigins.join(', ')}`);
// Basic middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use((0, cookie_parser_1.default)()); // Add cookie parser middleware
app.use((0, morgan_1.default)('dev'));
// Security middleware
app.use((0, helmet_1.default)()); // Add security headers
app.use((0, compression_1.default)()); // Compress responses
app.use(validation_middleware_1.default.securityHeaders); // Add additional security headers
app.use(validation_middleware_1.default.sanitizeUrlParams); // Sanitize URL parameters
// Caching middleware for public endpoints
app.use(apiCache_middleware_1.default.addCacheHeaders()); // Add cache headers to responses
// Serve static files from uploads directory
app.use('/uploads', express_1.default.static(path_1.default.join(__dirname, '../uploads')));
// Apply general API rate limiting to all routes
app.use('/api', rateLimiting_middleware_1.default.apiLimiter);
// Routes
// Legacy routes (token in Authorization header)
app.use('/api/auth', rateLimiting_middleware_1.default.authLimiter, auth_1.default);
// New secure routes (token in HTTP-only cookie)
// Apply CSRF protection to secure routes
app.use('/api/v2', csrf_middleware_1.generateCsrfToken); // Generate CSRF token for all secure routes
app.use('/api/v2', csrf_middleware_1.validateCsrfToken); // Validate CSRF token for all secure routes
app.use('/api/v2/auth', rateLimiting_middleware_1.default.authLimiter, auth_cookie_routes_1.default);
// app.use('/api/v2/admin', adminCookieRoutes); // Temporarily disabled
// Register admin cookie routes at the root level as well for backward compatibility
// app.use('/api/admin', adminCookieRoutes); // Temporarily disabled
// Shared routes with caching for public endpoints
app.use('/api/scholarships', apiCache_middleware_1.default.cacheApiResponse(300), validation_middleware_1.default.sanitizeRequestBody, scholarship_routes_1.default);
app.use('/api/users', validation_middleware_1.default.sanitizeRequestBody, user_routes_1.default);
app.use('/api/messages', rateLimiting_middleware_1.default.contactFormLimiter, validation_middleware_1.default.sanitizeRequestBody, messages_1.default);
app.use('/api/newsletter', rateLimiting_middleware_1.default.contactFormLimiter, validation_middleware_1.default.sanitizeRequestBody, newsletter_1.default);
// Admin password routes must be registered before admin routes to avoid authentication middleware
// app.use('/api/admin/password', rateLimiting.passwordResetLimiter, validation.sanitizeRequestBody, adminPasswordRoutes); // Temporarily disabled
// Two-factor authentication routes
app.use('/api/2fa', rateLimiting_middleware_1.default.authLimiter, validation_middleware_1.default.sanitizeRequestBody, twoFactor_routes_1.default);
// Health check endpoint with caching
app.get('/api/health', apiCache_middleware_1.default.cacheApiResponse(60), (req, res) => {
    res.json({
        status: 'ok',
        message: 'Server is running',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0'
    });
});
// Test database connection endpoint
app.get('/api/test-db', async (req, res) => {
    try {
        // Test database connection
        const isConnected = await (0, database_1.testConnection)();
        if (!isConnected) {
            throw new Error('Database connection test failed');
        }
        // Test model queries
        const adminCount = await Admin_1.Admin.count();
        const { User } = await Promise.resolve().then(() => __importStar(require('./models/User')));
        const userCount = await User.count();
        const { Scholarship } = await Promise.resolve().then(() => __importStar(require('./models/Scholarship')));
        const scholarshipCount = await Scholarship.count();
        res.json({
            success: true,
            message: 'Database connection is working properly',
            data: {
                adminCount,
                userCount,
                scholarshipCount
            }
        });
    }
    catch (error) {
        console.error('Database test error:', error);
        res.status(500).json({
            success: false,
            message: 'Database connection test failed',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
// Cache statistics endpoint (admin only)
app.get('/api/admin/cache-stats', (req, res) => {
    if (!req.user || !req.user.isMainAdmin) {
        return res.status(403).json({
            success: false,
            message: 'Access denied',
            error: 'Admin privileges required'
        });
    }
    res.json({
        success: true,
        message: 'Cache statistics retrieved successfully',
        data: {
            apiCache: apiCache_middleware_1.default.getCacheStats()
            // Additional cache stats can be added here
        }
    });
});
// Error handling middleware
const errorHandler = (err, req, res, next) => {
    console.error(err.stack);
    // Log the error
    const { apiLogger } = require('./utils/logger');
    apiLogger.error(`Unhandled error: ${err.message}`, {
        stack: err.stack,
        path: req.originalUrl,
        method: req.method,
        ip: req.ip,
        user: req.user ? { id: req.user.id, role: req.user.role } : undefined
    });
    // Send error response
    res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? err.message : undefined,
        requestId: req.headers['x-request-id'] || undefined,
        timestamp: new Date().toISOString()
    });
};
app.use(errorHandler);
// Import cleanup utilities
// import { performDataCleanup } from './utils/cleanupUtils'; // Temporarily disabled
// Initialize database and start server
const PORT = process.env.PORT || 5000;
// Start server
const startServer = async () => {
    try {
        // Test database connection
        const isConnected = await (0, database_1.testConnection)();
        if (!isConnected) {
            throw new Error('Database connection failed');
        }
        console.log('PostgreSQL database connection established successfully.');
        // Check if main admin exists
        const mainAdmin = await Admin_1.Admin.findMainAdmin();
        // Only create admin if none exists (this should already be handled by migration)
        if (!mainAdmin) {
            console.log('Creating main admin...');
            await Admin_1.Admin.create({
                name: 'Main Admin',
                email: '<EMAIL>',
                password: 'admin123',
                role: 'super_admin',
                privileges: ['all'],
                isMainAdmin: true,
                failedLoginAttempts: 0,
                twoFactorEnabled: false
            });
            console.log('Main admin created successfully.');
        }
        // Run data cleanup to fix any duplicate data issues
        // Temporarily disabled to fix server startup issues
        // console.log('Running data cleanup process...');
        // await performDataCleanup();
        // Start server
        app.listen(PORT, () => {
            console.log(`Server is running on port ${PORT}`);
        });
    }
    catch (error) {
        console.error('Failed to start server:', error);
        await (0, database_1.closeDatabase)();
        process.exit(1);
    }
};
// Handle application shutdown
process.on('SIGINT', async () => {
    await (0, database_1.closeDatabase)();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    await (0, database_1.closeDatabase)();
    process.exit(0);
});
startServer();
