import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { UserPayload } from '../types/express';
import { Admin } from '../models/Admin';
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Get all admins (main admin only)
export const getAdmins = async (req: Request, res: Response) => {
  try {
    const admins = await Admin.findAll();
    res.status(200).json(admins);
  } catch (error) {
    console.error('Error getting admins:', error);
    res.status(500).json({ message: 'Error getting admins' });
  }
};

// Create a new admin (main admin only)
export const createAdmin = async (req: Request, res: Response) => {
  try {
    const { name, email, password, privileges, isMainAdmin = false } = req.body;

    // Check if admin with this email already exists
    const existingAdmin = await prisma.admin.findUnique({
      where: { email }
    });

    if (existingAdmin) {
      return res.status(409).json({ message: 'Admin with this email already exists' });
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create the admin
    const admin = await prisma.admin.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: 'admin',
        privileges: Array.isArray(privileges) ? JSON.stringify(privileges) : privileges,
        isMainAdmin: Boolean(isMainAdmin),
        twoFactorEnabled: false
      }
    });

    // Return admin data (excluding password)
    res.status(201).json({
      id: admin.id,
      name: admin.name,
      email: admin.email,
      role: admin.role,
      privileges: admin.privileges,
      isMainAdmin: admin.isMainAdmin,
      twoFactorEnabled: admin.twoFactorEnabled,
      createdAt: admin.createdAt,
      updatedAt: admin.updatedAt
    });
  } catch (error) {
    console.error('Error creating admin:', error);
    res.status(500).json({ message: 'Error creating admin' });
  }
};

// Update an admin (main admin only)
export const updateAdmin = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, email, privileges, isMainAdmin } = req.body;

    // Check if admin exists
    const admin = await prisma.admin.findUnique({
      where: { id: Number(id) }
    });

    if (!admin) {
      return res.status(404).json({ message: 'Admin not found' });
    }

    // Prepare update data
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (email !== undefined) updateData.email = email;
    if (privileges !== undefined) {
      updateData.privileges = Array.isArray(privileges) ? JSON.stringify(privileges) : privileges;
    }
    if (isMainAdmin !== undefined) updateData.isMainAdmin = Boolean(isMainAdmin);

    // Update the admin
    const updatedAdmin = await prisma.admin.update({
      where: { id: Number(id) },
      data: updateData
    });

    // Return updated admin data
    res.status(200).json({
      id: updatedAdmin.id,
      name: updatedAdmin.name,
      email: updatedAdmin.email,
      role: updatedAdmin.role,
      privileges: updatedAdmin.privileges,
      isMainAdmin: updatedAdmin.isMainAdmin,
      twoFactorEnabled: updatedAdmin.twoFactorEnabled,
      updatedAt: updatedAdmin.updatedAt
    });
  } catch (error) {
    console.error('Error updating admin:', error);
    res.status(500).json({ message: 'Error updating admin' });
  }
};

// Delete an admin (main admin only)
export const deleteAdmin = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Check if admin exists
    const admin = await prisma.admin.findUnique({
      where: { id: Number(id) }
    });

    if (!admin) {
      return res.status(404).json({ message: 'Admin not found' });
    }

    // Prevent deleting the last main admin
    if (admin.isMainAdmin) {
      const mainAdminCount = await prisma.admin.count({
        where: { isMainAdmin: true }
      });

      if (mainAdminCount <= 1) {
        return res.status(400).json({ message: 'Cannot delete the last main admin' });
      }
    }

    // Delete the admin
    await prisma.admin.delete({
      where: { id: Number(id) }
    });

    res.json({ message: 'Admin deleted successfully' });
  } catch (error) {
    console.error('Error deleting admin:', error);
    res.status(500).json({ message: 'Error deleting admin' });
  }
};

// Get admin by ID (main admin only)
export const getAdminById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const admin = await prisma.admin.findUnique({
      where: { id: Number(id) },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        privileges: true,
        isMainAdmin: true,
        twoFactorEnabled: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!admin) {
      return res.status(404).json({ message: 'Admin not found' });
    }

    res.json(admin);
  } catch (error) {
    console.error('Error getting admin:', error);
    res.status(500).json({ message: 'Error getting admin' });
  }
};
